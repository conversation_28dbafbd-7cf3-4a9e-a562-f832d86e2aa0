using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;

namespace NetworkManagement.ViewModels
{
    public partial class UserDialogViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IAuthService _authService;
        private readonly INetworkService _networkService;

        [ObservableProperty]
        private string title = "إضافة مستخدم جديد";

        [ObservableProperty]
        private string username = string.Empty;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string password = string.Empty;

        [ObservableProperty]
        private string confirmPassword = string.Empty;

        [ObservableProperty]
        private string role = "technician";

        [ObservableProperty]
        private string selectedNetworkId = string.Empty;

        [ObservableProperty]
        private List<Network> availableNetworks = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool showPasswordFields = true;

        // الأدوار المتاحة حسب صلاحيات المستخدم الحالي
        public string[] Roles => GetAvailableRoles();

        private User? _editingUser;
        public bool IsEditMode => _editingUser != null;

        public event EventHandler<User>? UserSaved;
        public event EventHandler? DialogClosed;

        public UserDialogViewModel(IUserService userService, IAuthService authService, INetworkService networkService)
        {
            _userService = userService;
            _authService = authService;
            _networkService = networkService;
            _ = LoadNetworksAsync();
        }

        public void SetEditUser(User user)
        {
            _editingUser = user;
            Title = "تحرير المستخدم";
            ShowPasswordFields = false; // Hide password fields in edit mode

            Username = user.Username;
            Name = user.Name;
            Role = user.Role;
            SelectedNetworkId = user.NetworkId ?? string.Empty;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SaveAsync()
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsLoading = true;
                ErrorMessage = string.Empty;

                var user = _editingUser ?? new User();

                user.Username = Username.Trim();
                user.Name = Name.Trim();
                user.Role = Role;
                // تعيين NetworkId - فحص الدور أولاً
                if (Role.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                {
                    // Admin دائماً بدون شبكة محددة للحصول على صلاحيات كاملة
                    user.NetworkId = null;
                }
                else
                {
                    // المستخدمون الآخرون - استخدام الشبكة المختارة أو شبكة المستخدم الحالي
                    if (!string.IsNullOrWhiteSpace(SelectedNetworkId))
                    {
                        // استخدام الشبكة المختارة
                        user.NetworkId = SelectedNetworkId;
                    }
                    else
                    {
                        // إذا كان المستخدم الحالي Network Manager، استخدم شبكته للمستخدمين الجدد
                        var currentUser = _authService.CurrentUser;
                        if (_authService.IsAdmin)
                        {
                            // للأدمن: يمكن ترك الحقل فارغ
                            user.NetworkId = null;
                        }
                        else if (_authService.IsNetworkManager && !string.IsNullOrEmpty(currentUser?.NetworkId))
                        {
                            // للـ Network Manager: استخدم شبكته تلقائياً
                            user.NetworkId = currentUser.NetworkId;
                        }
                        else
                        {
                            // لباقي المستخدمين: يمكن ترك الحقل فارغ
                            user.NetworkId = null;
                        }
                    }
                }

                if (_editingUser == null)
                {
                    // إضافة مستخدم جديد - كلمة المرور مطلوبة
                    user.Password = Password;
                    user.CreatedAt = DateTime.Now;
                    await _userService.CreateAsync(user);
                }
                else
                {
                    // تحديث مستخدم موجود - تحديث كلمة المرور فقط إذا تم إدخالها
                    if (!string.IsNullOrWhiteSpace(Password))
                    {
                        user.Password = Password;
                    }
                    await _userService.UpdateAsync(user);
                }

                UserSaved?.Invoke(this, user);
                DialogClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ المستخدم: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private bool ValidateInput()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(Username))
            {
                ErrorMessage = "اسم المستخدم مطلوب";
                return false;
            }

            if (string.IsNullOrWhiteSpace(Name))
            {
                ErrorMessage = "الاسم الكامل مطلوب";
                return false;
            }

            // Validate password for new users
            if (_editingUser == null)
            {
                if (string.IsNullOrWhiteSpace(Password))
                {
                    ErrorMessage = "كلمة المرور مطلوبة";
                    return false;
                }

                if (Password.Length < 6)
                {
                    ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
                    return false;
                }

                if (Password != ConfirmPassword)
                {
                    ErrorMessage = "كلمة المرور وتأكيدها غير متطابقتين";
                    return false;
                }
            }
            else
            {
                // For existing users, validate password only if provided
                if (!string.IsNullOrWhiteSpace(Password))
                {
                    if (Password.Length < 6)
                    {
                        ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
                        return false;
                    }

                    if (Password != ConfirmPassword)
                    {
                        ErrorMessage = "كلمة المرور وتأكيدها غير متطابقتين";
                        return false;
                    }
                }
            }

            // Validate username format
            if (Username.Trim().Length < 3)
            {
                ErrorMessage = "اسم المستخدم يجب أن يكون 3 أحرف على الأقل";
                return false;
            }

            // فحص صلاحية إنشاء الدور المحدد
            if (!ValidateRolePermission())
            {
                return false;
            }

            return true;
        }

        private async System.Threading.Tasks.Task LoadNetworksAsync()
        {
            try
            {
                // استخدام NetworkHelper لتحميل الشبكات مع تطبيق الصلاحيات
                var (networks, defaultNetworkId) = await NetworkHelper.LoadNetworksForSelectionAsync(
                    _authService,
                    _networkService);

                AvailableNetworks = networks;

                // تعيين الشبكة الافتراضية
                if (!string.IsNullOrEmpty(defaultNetworkId))
                {
                    SelectedNetworkId = defaultNetworkId;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading networks: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص صلاحية إنشاء الدور المحدد
        /// </summary>
        private bool ValidateRolePermission()
        {
            var availableRoles = GetAvailableRoles();

            if (!availableRoles.Contains(Role, StringComparer.OrdinalIgnoreCase))
            {
                ErrorMessage = $"ليس لديك صلاحية لإنشاء مستخدم بدور '{Role}'. الأدوار المتاحة لك: {string.Join(", ", availableRoles)}";
                return false;
            }

            return true;
        }

        /// <summary>
        /// الحصول على الأدوار المتاحة حسب صلاحيات المستخدم الحالي
        /// </summary>
        private string[] GetAvailableRoles()
        {
            var currentUser = _authService.CurrentUser;
            if (currentUser == null) return Array.Empty<string>();

            // Super Admin يمكنه إنشاء جميع الأدوار
            if (_authService.IsSuperAdmin)
            {
                return new[] { "admin", "manager", "technician" };
            }

            // Network Manager يمكنه إنشاء أدوار أقل من دوره فقط
            if (_authService.IsNetworkManager)
            {
                return new[] { "technician" };
            }

            // باقي المستخدمين لا يمكنهم إنشاء مستخدمين
            return Array.Empty<string>();
        }
    }
}
