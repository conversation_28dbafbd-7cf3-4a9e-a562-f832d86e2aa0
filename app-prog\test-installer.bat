@echo off
chcp 65001 >nul
echo ========================================
echo    اختبار مثبت Shabaka Pro
echo    Testing Shabaka Pro Installer
echo ========================================
echo.

if not exist "Output\ShabakaPro-Setup-v1.0.0.exe" (
    echo ❌ ملف المثبت غير موجود
    echo ❌ Installer file not found
    echo.
    echo يرجى تشغيل build-shabaka-pro.bat أولاً
    echo Please run build-shabaka-pro.bat first
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف المثبت
echo ✅ Installer file found
echo.

:: عرض معلومات الملف
echo 📊 معلومات الملف:
echo 📊 File information:
for %%A in ("Output\ShabakaPro-Setup-v1.0.0.exe") do (
    echo    الحجم / Size: %%~zA bytes
    echo    التاريخ / Date: %%~tA
)
echo.

echo 🔍 خيارات الاختبار:
echo 🔍 Testing options:
echo.
echo [1] تشغيل المثبت عادي (Normal installation)
echo [2] تشغيل المثبت صامت (Silent installation)
echo [3] عرض معلومات المثبت فقط (Show installer info only)
echo [4] إلغاء (Cancel)
echo.

set /p choice="اختر رقم الخيار / Choose option number: "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل المثبت العادي...
    echo 🚀 Running normal installation...
    start "" "Output\ShabakaPro-Setup-v1.0.0.exe"
    echo ✓ تم تشغيل المثبت
    echo ✓ Installer launched
) else if "%choice%"=="2" (
    echo.
    echo 🤫 تشغيل المثبت الصامت...
    echo 🤫 Running silent installation...
    echo تحذير: سيتم التثبيت بدون واجهة مستخدم
    echo Warning: Installation will proceed without user interface
    echo.
    set /p confirm="هل أنت متأكد؟ (y/n) / Are you sure? (y/n): "
    if /i "!confirm!"=="y" (
        start "" "Output\ShabakaPro-Setup-v1.0.0.exe" /SILENT
        echo ✓ تم بدء التثبيت الصامت
        echo ✓ Silent installation started
    ) else (
        echo ❌ تم إلغاء العملية
        echo ❌ Operation cancelled
    )
) else if "%choice%"=="3" (
    echo.
    echo 📋 معلومات المثبت:
    echo 📋 Installer information:
    echo    الاسم / Name: Shabaka Pro Setup
    echo    الإصدار / Version: 1.0.0
    echo    النوع / Type: Inno Setup Installer
    echo    المعمارية / Architecture: x64
    echo    اللغات / Languages: Arabic, English
    echo    متطلبات النظام / System Requirements: Windows 7+
    echo.
    echo ✅ تم عرض المعلومات
    echo ✅ Information displayed
) else if "%choice%"=="4" (
    echo.
    echo ❌ تم إلغاء العملية
    echo ❌ Operation cancelled
) else (
    echo.
    echo ❌ خيار غير صحيح
    echo ❌ Invalid option
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
