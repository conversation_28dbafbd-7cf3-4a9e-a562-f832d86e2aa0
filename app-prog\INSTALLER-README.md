# 📦 دليل المثبت الاحترافي - Shabaka Pro

## 🎯 نظرة عامة

تم إنشاء مثبت احترافي لتطبيق **Shabaka Pro** باستخدام **Inno Setup** يوفر تجربة تثبيت سلسة ومتوافقة مع جميع إصدارات Windows من Windows 7 فما فوق.

## ✨ مميزات المثبت

### 🌟 المميزات الأساسية
- **واجهة احترافية** - تصميم حديث وأنيق
- **دعم اللغة العربية والإنجليزية** - واجهة ثنائية اللغة
- **Self-Contained** - يحتوي على جميع المكتبات المطلوبة
- **متوافق مع Windows 7+** - يعمل على جميع إصدارات Windows الحديثة
- **حجم محسن** - ضغط عالي الجودة لتقليل حجم الملف

### 🔧 المميزات التقنية
- **كشف الإصدارات السابقة** - ترقية تلقائية للإصدارات القديمة
- **تثبيت صامت** - إمكانية التثبيت بدون تدخل المستخدم
- **إنشاء اختصارات** - سطح المكتب وقائمة ابدأ
- **تسجيل في Registry** - حفظ معلومات التثبيت
- **إلغاء تثبيت كامل** - إزالة نظيفة للتطبيق

## 📁 ملفات المثبت

```
Output/
└── ShabakaPro-Setup-v1.0.0.exe    # ملف المثبت النهائي (حوالي 150 MB)
```

## 🚀 كيفية استخدام المثبت

### للمطورين - إنشاء المثبت:
```bash
# 1. بناء المثبت
build-shabaka-pro.bat

# 2. تشغيل المثبت للاختبار
run-installer.bat
```

### للمستخدمين النهائيين:
1. **تحميل الملف**: `ShabakaPro-Setup-v1.0.0.exe`
2. **تشغيل المثبت**: نقر مزدوج على الملف
3. **اتباع التعليمات**: اختيار اللغة ومسار التثبيت
4. **انتظار التثبيت**: سيتم تثبيت التطبيق تلقائياً
5. **تشغيل التطبيق**: من سطح المكتب أو قائمة ابدأ

## ⚙️ خيارات التثبيت المتقدمة

### التثبيت الصامت:
```cmd
ShabakaPro-Setup-v1.0.0.exe /SILENT
```

### التثبيت الصامت مع عدم إعادة التشغيل:
```cmd
ShabakaPro-Setup-v1.0.0.exe /SILENT /NORESTART
```

### تحديد مسار التثبيت:
```cmd
ShabakaPro-Setup-v1.0.0.exe /DIR="C:\MyPrograms\ShabakaPro"
```

## 🔍 متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 7 SP1 أو أحدث
- **المعمارية**: x64 (64-bit)
- **الذاكرة**: 2 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة
- **.NET Runtime**: سيتم تثبيته تلقائياً إذا لم يكن موجوداً

### الموصى به:
- **نظام التشغيل**: Windows 10/11
- **الذاكرة**: 4 GB RAM أو أكثر
- **مساحة القرص**: 1 GB مساحة فارغة

## 🛠️ استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها:

#### 1. خطأ "يتطلب صلاحيات المدير"
**الحل**: تشغيل المثبت كمدير
```
انقر بالزر الأيمن على الملف → تشغيل كمدير
```

#### 2. خطأ ".NET Runtime غير موجود"
**الحل**: المثبت سيعرض رسالة تنبيه ويمكن تحميل .NET من موقع Microsoft

#### 3. خطأ "مساحة القرص غير كافية"
**الحل**: تحرير مساحة على القرص أو اختيار مسار تثبيت آخر

#### 4. فشل في إلغاء تثبيت الإصدار السابق
**الحل**: إلغاء تثبيت الإصدار السابق يدوياً من لوحة التحكم

## 📋 سجل التغييرات

### الإصدار 1.0.0
- ✅ إنشاء المثبت الأولي
- ✅ دعم Windows 7+
- ✅ واجهة ثنائية اللغة
- ✅ تثبيت Self-Contained
- ✅ كشف الإصدارات السابقة
- ✅ إنشاء اختصارات تلقائياً

## 🔐 الأمان

- **التوقيع الرقمي**: غير مطلوب للاستخدام الداخلي
- **فحص الفيروسات**: يُنصح بفحص الملف قبل التوزيع
- **التحقق من التكامل**: يمكن إنشاء hash للملف للتحقق

## 📞 الدعم التقني

في حالة مواجهة مشاكل في التثبيت:

1. **تحقق من متطلبات النظام**
2. **تشغيل المثبت كمدير**
3. **تعطيل برامج مكافحة الفيروسات مؤقتاً**
4. **التواصل مع فريق الدعم**

## 📝 ملاحظات مهمة

- **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية من البيانات قبل الترقية
- **إغلاق التطبيق**: تأكد من إغلاق التطبيق قبل التثبيت أو الترقية
- **الشبكة**: لا يتطلب اتصال بالإنترنت للتثبيت
- **الترخيص**: التطبيق مجاني للاستخدام الداخلي

---

**تم إنشاء هذا المثبت بواسطة Augment Agent**  
**© 2024 Shabaka Pro. جميع الحقوق محفوظة.**
