# نظام إدارة الشبكات - Network Management System

تطبيق سطح مكتب لإدارة الشبكات والأجهزة مبني بـ C# WPF

## المتطلبات

- .NET 6.0 أو أحدث
- Windows 7 أو أحدث
- Visual Studio 2022 أو Visual Studio Code

## المميزات

### 🏠 لوحة التحكم
- عرض إحصائيات شاملة للنظام
- مراقبة حالة الأجهزة والمواقع
- تتبع المهام والمشتريات

### 🖥️ إدارة الأجهزة
- إضافة وتحرير وحذف الأجهزة
- تتبع حالة الأجهزة (نشط/غير نشط/صيانة)
- معلومات تفصيلية لكل جهاز (IP، الموقع، نوع الاتصال، إلخ)

### 📍 إدارة المواقع
- إدارة مواقع الشبكة
- معلومات GPS والعناوين
- ربط الأجهزة بالمواقع

### 👥 إدارة المستخدمين
- إدارة حسابات المستخدمين
- أدوار مختلفة (مدير، مشرف، فني)
- تحكم في الصلاحيات

### ✅ إدارة المهام
- إنشاء وتتبع المهام
- حالات مختلفة للمهام (معلق، قيد التنفيذ، مكتمل)
- تعيين المهام للمستخدمين

### 💰 إدارة المشتريات
- تسجيل المشتريات والفواتير
- تتبع الإنفاق الشهري
- تصنيف المشتريات

### 📦 إدارة المخزون
- تتبع المخزون والكميات
- تنبيهات المخزون المنخفض
- إدارة الموردين

### 📊 التقارير
- تقارير شاملة عن جميع جوانب النظام
- إحصائيات مفصلة
- إمكانية التصدير

## كيفية التشغيل

### الطريقة الأولى: ملف التثبيت الاحترافي (مُوصى به)
```bash
# بناء ملف التثبيت
build-installer.bat

# تشغيل ملف التثبيت
Installer\ShabakaPro-Setup-v1.0.0.exe
```

### الطريقة الثانية: التشغيل من الكود المصدري
```bash
# تأكد من تثبيت .NET 6.0 SDK
dotnet --version

# استعادة الحزم
dotnet restore

# بناء التطبيق
dotnet build

# تشغيل التطبيق
dotnet run
```

### الطريقة الثالثة: التشغيل السريع
```bash
# انقر نقراً مزدوجاً على
run.bat
```

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** password

## هيكل المشروع

```
NetworkManagement/
├── Models/              # نماذج البيانات
├── Data/               # قاعدة البيانات وDbContext
├── Services/           # خدمات الأعمال
├── ViewModels/         # ViewModels للواجهات
├── Views/              # واجهات المستخدم (XAML)
├── Converters/         # محولات البيانات للواجهة
└── App.xaml           # إعدادات التطبيق الرئيسية
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تُحفظ في:
```
Data/shabakaty.db
```

## ملف التثبيت الاحترافي

### 🚀 إنشاء ملف التثبيت
```bash
# بناء ملف التثبيت بخطوة واحدة
build-shabaka-pro.bat
```

### 🎯 تشغيل المثبت
```bash
# تشغيل المثبت بعد إنشائه
run-installer.bat
```

### 📦 مميزات ملف التثبيت
- **واجهة احترافية** - تصميم حديث وأنيق
- **دعم اللغة العربية والإنجليزية** - واجهة ثنائية اللغة
- **تثبيت تلقائي للتبعيات** - يتضمن .NET 6.0 Desktop Runtime
- **Self-Contained** - لا يحتاج تثبيت إضافي على الجهاز المستهدف
- **متوافق مع Windows 7+** - يعمل على جميع إصدارات Windows الحديثة
- **إنشاء اختصارات** - سطح المكتب وقائمة ابدأ
- **إلغاء تثبيت كامل** - إزالة نظيفة للتطبيق
- **كشف الإصدارات السابقة** - ترقية تلقائية

### 📋 متطلبات إنشاء ملف التثبيت
- **Inno Setup** - تحميل من https://jrsoftware.org/isinfo.php
- **.NET 6.0 SDK** - للبناء والنشر

### 📁 ملفات التثبيت
```
├── ShabakaPro-Setup.iss          # سكريبت Inno Setup الرئيسي
├── build-shabaka-pro.bat         # ملف بناء المثبت
├── run-installer.bat             # ملف تشغيل المثبت
└── Output/
    └── ShabakaPro-Setup-v1.0.0.exe  # ملف المثبت النهائي
```

## التقنيات المستخدمة

- **C# .NET 6.0** - اللغة الأساسية
- **WPF** - واجهة المستخدم
- **Entity Framework Core** - ORM لقاعدة البيانات
- **MySQL** - قاعدة البيانات
- **Material Design** - تصميم الواجهة
- **MVVM Pattern** - نمط التصميم
- **CommunityToolkit.Mvvm** - مكتبة MVVM
- **Inno Setup** - نظام إنشاء ملفات التثبيت الاحترافية

## المطورين

تم تطوير هذا التطبيق لإدارة الشبكات والأجهزة بواسطة Augment Agent.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
