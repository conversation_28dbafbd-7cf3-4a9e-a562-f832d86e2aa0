using System;
using System.Threading.Tasks;

namespace NetworkManagement.Services
{
    public interface ISettingsService
    {
        /// <summary>
        /// حفظ بيانات تسجيل الدخول للتذكر
        /// </summary>
        Task SaveRememberedCredentialsAsync(string username, string password);

        /// <summary>
        /// الحصول على بيانات تسجيل الدخول المحفوظة
        /// </summary>
        Task<(string? username, string? password)> GetRememberedCredentialsAsync();

        /// <summary>
        /// مسح بيانات تسجيل الدخول المحفوظة
        /// </summary>
        Task ClearRememberedCredentialsAsync();

        /// <summary>
        /// التحقق من وجود بيانات تسجيل دخول محفوظة
        /// </summary>
        Task<bool> HasRememberedCredentialsAsync();

        /// <summary>
        /// الحصول على جميع الإعدادات
        /// </summary>
        Task<AppSettings> GetSettingsAsync();

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        Task SaveSettingsAsync(object settings);

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        Task ResetSettingsAsync();

        /// <summary>
        /// تحديث تاريخ آخر نسخة احتياطية
        /// </summary>
        Task UpdateLastBackupDateAsync(DateTime date);

        /// <summary>
        /// حفظ مسار قاعدة البيانات
        /// </summary>
        Task SaveDatabasePathAsync(string databasePath);

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        Task<string> GetDatabasePathAsync();

        /// <summary>
        /// التحقق من صحة مسار قاعدة البيانات
        /// </summary>
        bool IsDatabasePathValid(string path);

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        Task<bool> TestDatabaseConnectionAsync(string databasePath = "");

        /// <summary>
        /// تحميل إعدادات قاعدة البيانات MySQL
        /// </summary>
        AppSettings LoadSettings();
    }

    public class AppSettings
    {
        public bool RememberLogin { get; set; } = true;
        public string? DefaultNetwork { get; set; }
        public int PingTimeout { get; set; } = 5;
        public bool ShowNotifications { get; set; } = true;
        public string? Theme { get; set; } = "Light";
        public string? Language { get; set; } = "العربية";
        public string? BackupLocation { get; set; }
        public bool AutoBackupEnabled { get; set; } = false;
        public int AutoBackupDays { get; set; } = 7;
        public DateTime? LastBackupDate { get; set; }
        public string MySQLServer { get; set; } = "localhost";
        public string MySQLDatabase { get; set; } = "shabakaty";
        public string MySQLUser { get; set; } = "root";
        public string MySQLPassword { get; set; } = "Aa774070632a";
    }
}
