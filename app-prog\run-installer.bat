@echo off
chcp 65001 >nul
echo ========================================
echo    تشغيل مثبت Shabaka Pro
echo    Running Shabaka Pro Installer
echo ========================================
echo.

if exist "Output\ShabakaPro-Setup-v1.0.0.exe" (
    echo 🚀 تشغيل المثبت...
    echo 🚀 Running installer...
    echo.
    start "" "Output\ShabakaPro-Setup-v1.0.0.exe"
    echo ✓ تم تشغيل المثبت بنجاح
    echo ✓ Installer launched successfully
) else (
    echo ❌ لم يتم العثور على ملف المثبت
    echo ❌ Installer file not found
    echo.
    echo يرجى تشغيل build-shabaka-pro.bat أولاً لإنشاء المثبت
    echo Please run build-shabaka-pro.bat first to create the installer
    echo.
    pause
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul