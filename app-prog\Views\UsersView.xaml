<UserControl x:Class="NetworkManagement.Views.UsersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <TextBlock Grid.Row="0" Text="إدارة المستخدمين" Style="{StaticResource PageHeaderStyle}"/>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="البحث في المستخدمين..."
                        materialDesign:HintAssist.IsFloating="False"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        MinWidth="250" Margin="0,0,15,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchUsersCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Role Filter -->
                <ComboBox Grid.Column="1"
                         materialDesign:HintAssist.Hint="فلترة حسب الدور"
                         ItemsSource="{Binding Roles}"
                         SelectedItem="{Binding SelectedRole}"
                         MinWidth="150" Margin="0,0,15,0"/>

                <!-- Spacer -->
                <Grid Grid.Column="2"/>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <Button Command="{Binding SearchUsersCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="بحث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding LoadUsersCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding AddUserCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Visibility="{Binding CanAddUsers, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة مستخدم"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Users List -->
        <materialDesign:Card Grid.Row="2">
            <Grid>
                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            VerticalAlignment="Top" Height="4"/>

                <!-- Data Grid -->
                <DataGrid ItemsSource="{Binding Users}"
                         SelectedItem="{Binding SelectedUser}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single"
                         Margin="10">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="150"/>
                        <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <Trigger Property="Text" Value="admin">
                                            <Setter Property="Foreground" Value="#F44336"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </Trigger>
                                        <Trigger Property="Text" Value="manager">
                                            <Setter Property="Foreground" Value="#FF9800"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </Trigger>
                                        <Trigger Property="Text" Value="technician">
                                            <Setter Property="Foreground" Value="#4CAF50"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الشبكة" Binding="{Binding Network.Name, TargetNullValue='غير محدد'}" Width="120"/>
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat=dd/MM/yyyy}" Width="120"/>

                        <!-- Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="تحرير"
                                               Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignFlatButton}"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               Margin="0,0,5,0"
                                               Visibility="{Binding Converter={StaticResource CanEditUserConverter}}"/>
                                        <Button Content="حذف"
                                               Command="{Binding DataContext.DeleteUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignFlatButton}"
                                               Foreground="Red"
                                               Visibility="{Binding Converter={StaticResource CanDeleteUserConverter}}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center"
                           Visibility="{Binding Users.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="AccountGroup" Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text="لا يوجد مستخدمين"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    <TextBlock Text="انقر على 'إضافة مستخدم' لإضافة المستخدم الأول"
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
