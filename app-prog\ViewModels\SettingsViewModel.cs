using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Helpers;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Data;
using Microsoft.Win32;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Task = System.Threading.Tasks.Task;
using AppSettings = NetworkManagement.Models.AppSettings;

namespace NetworkManagement.ViewModels
{
    public partial class SettingsViewModel : ObservableObject
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthService _authService;
        private readonly IThemeService _themeService;
        private readonly ILocalizationService _localizationService;

        // Threading control
        private readonly SemaphoreSlim _loadSemaphore = new(1, 1);
        private CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string applicationName = "نظام إدارة الشبكات";

        [ObservableProperty]
        private string applicationVersion = "1.0.0";

        [ObservableProperty]
        private long databaseSize = 0;

        [ObservableProperty]
        private string databaseSizeDisplay = "";

        [ObservableProperty]
        private int totalDevices = 0;

        [ObservableProperty]
        private int totalSites = 0;

        [ObservableProperty]
        private int totalUsers = 0;

        [ObservableProperty]
        private int totalPurchases = 0;

        [ObservableProperty]
        private int totalInventoryItems = 0;

        [ObservableProperty]
        private DateTime lastBackupDate = DateTime.MinValue;

        [ObservableProperty]
        private string lastBackupDateDisplay = "لم يتم إنشاء نسخة احتياطية";

        [ObservableProperty]
        private bool autoBackupEnabled = false;

        [ObservableProperty]
        private int autoBackupDays = 7;

        [ObservableProperty]
        private string backupLocation = "";

        [ObservableProperty]
        private bool rememberLoginEnabled = true;

        [ObservableProperty]
        private string defaultNetwork = "";

        [ObservableProperty]
        private int pingTimeoutSeconds = 5;

        [ObservableProperty]
        private bool showNotifications = true;

        [ObservableProperty]
        private string theme = "Light";

        [ObservableProperty]
        private string language = "العربية";

        [ObservableProperty]
        private string mySQLServer = "localhost";

        [ObservableProperty]
        private string mySQLDatabase = "shabakaty";

        [ObservableProperty]
        private string mySQLUser = "root";

        [ObservableProperty]
        private string mySQLPassword = "Aa774070632a";

        [ObservableProperty]
        private bool isDatabaseConnected = false;

        [ObservableProperty]
        private string databaseConnectionStatus = "غير متصل";

        public string[] AvailableThemes { get; } = { "Light", "Dark" };
        public string[] AvailableLanguages { get; } = { "العربية", "English" };
        public int[] AutoBackupOptions { get; } = { 1, 3, 7, 14, 30 };

        // خصائص الصلاحيات
        public bool CanAccessSettings => SettingsHelper.CanAccessSettings(_authService);
        public bool CanCreateBackup => SettingsHelper.CanCreateBackup(_authService);
        public bool CanRestoreBackup => SettingsHelper.CanRestoreBackup(_authService);
        public bool CanUpdateDatabaseStructure => SettingsHelper.CanUpdateDatabaseStructure(_authService);
        public bool CanEditDatabaseSettings => SettingsHelper.CanEditDatabaseSettings(_authService);

        public SettingsViewModel(IServiceProvider serviceProvider, IAuthService authService, IThemeService themeService, ILocalizationService localizationService)
        {
            _serviceProvider = serviceProvider;
            _authService = authService;
            _themeService = themeService;
            _localizationService = localizationService;

            // الاشتراك في تغييرات المستخدم
            _authService.UserChanged += OnUserChanged;

            _ = LoadSettingsAsync();
        }

        [RelayCommand]
        private async Task LoadSettingsAsync()
        {
            // إلغاء العملية السابقة إن وجدت
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource = new CancellationTokenSource();
            }

            var cancellationToken = _loadCancellationTokenSource.Token;

            if (!await _loadSemaphore.WaitAsync(100, cancellationToken))
                return;

            try
            {
                IsLoading = true;

                // Load application info
                ApplicationName = "نظام إدارة الشبكات";
                ApplicationVersion = "1.0.0";

                // إنشاء scope جديد للحصول على الخدمات
                using var scope = _serviceProvider.CreateScope();
                var settingsService = scope.ServiceProvider.GetRequiredService<ISettingsService>();
                var databaseService = scope.ServiceProvider.GetRequiredService<IDatabaseService>();

                // Test database connection using helper
                var (isConnected, statusMessage) = await SettingsHelper.TestDatabaseConnectionAsync(
                    settingsService, cancellationToken);

                IsDatabaseConnected = isConnected;
                DatabaseConnectionStatus = statusMessage;

                // Get database info if connected
                if (isConnected)
                {
                    var (sizeInBytes, sizeDisplay) = await SettingsHelper.GetDatabaseInfoAsync(
                        databaseService, cancellationToken);
                    DatabaseSize = sizeInBytes;
                    DatabaseSizeDisplay = sizeDisplay;
                }

                // التحقق من الإلغاء
                cancellationToken.ThrowIfCancellationRequested();

                // Load statistics with permission filtering
                await LoadFilteredStatisticsAsync(cancellationToken);

                // Load settings using helper
                var settings = await SettingsHelper.LoadSettingsWithDefaultsAsync(
                    settingsService, cancellationToken);

                // Apply settings to properties
                ApplySettingsToProperties(settings);
            }
            catch (OperationCanceledException)
            {
                // تم إلغاء العملية - لا نفعل شيء
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"LoadSettingsAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();
            }
        }

        private async Task LoadFilteredStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var statistics = await SettingsHelper.LoadFilteredStatisticsAsync(
                    _serviceProvider, _authService, cancellationToken);

                TotalDevices = statistics.TotalDevices;
                TotalSites = statistics.TotalSites;
                TotalUsers = statistics.TotalUsers;
                TotalPurchases = statistics.TotalPurchases;
                TotalInventoryItems = statistics.TotalInventoryItems;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
                // تصفير الإحصائيات في حالة الخطأ
                TotalDevices = 0;
                TotalSites = 0;
                TotalUsers = 0;
                TotalPurchases = 0;
                TotalInventoryItems = 0;
            }
        }

        private void ApplySettingsToProperties(AppSettings settings)
        {
            RememberLoginEnabled = settings.RememberLogin;
            DefaultNetwork = settings.DefaultNetwork ?? "";
            PingTimeoutSeconds = settings.PingTimeout;
            ShowNotifications = settings.ShowNotifications;
            Theme = settings.Theme ?? "Light";
            Language = settings.Language ?? "العربية";

            // Load backup settings
            BackupLocation = settings.BackupLocation ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "NetworkManagement_Backups");
            AutoBackupEnabled = settings.AutoBackupEnabled;
            AutoBackupDays = settings.AutoBackupDays;

            // Load MySQL settings (only if user has permission)
            if (CanEditDatabaseSettings)
            {
                MySQLServer = settings.MySQLServer;
                MySQLDatabase = settings.MySQLDatabase;
                MySQLUser = settings.MySQLUser;
                MySQLPassword = settings.MySQLPassword;
            }

            if (settings.LastBackupDate.HasValue)
            {
                LastBackupDate = settings.LastBackupDate.Value;
                LastBackupDateDisplay = LastBackupDate.ToString("dd/MM/yyyy HH:mm");
            }
            else
            {
                LastBackupDateDisplay = "لم يتم إنشاء نسخة احتياطية";
            }
        }

        [RelayCommand]
        private async Task TestDatabaseConnectionAsync()
        {
            try
            {
                IsLoading = true;
                IsDatabaseConnected = false;
                DatabaseConnectionStatus = "جاري الاختبار...";

                // إنشاء scope جديد للحصول على SettingsService
                using var scope = _serviceProvider.CreateScope();
                var settingsService = scope.ServiceProvider.GetRequiredService<ISettingsService>();

                var (isConnected, statusMessage) = await SettingsHelper.TestDatabaseConnectionAsync(settingsService);

                IsDatabaseConnected = isConnected;
                DatabaseConnectionStatus = statusMessage;

                if (isConnected)
                {
                    MessageBox.Show(
                        "✅ الاتصال بقاعدة البيانات ناجح!",
                        "نجح الاختبار",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show(
                        "❌ فشل الاتصال بقاعدة البيانات.\n\nتحقق من:\n• إعدادات الخادم\n• صلاحيات الوصول\n• صحة بيانات الاتصال",
                        "فشل الاختبار",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                IsDatabaseConnected = false;
                DatabaseConnectionStatus = "غير متصل";
                MessageBox.Show($"فشل الاتصال بقاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"TestDatabaseConnectionAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task RepairDatabaseAsync()
        {
            // التحقق من صلاحية إصلاح قاعدة البيانات
            if (!CanUpdateDatabaseStructure)
            {
                SettingsHelper.ShowSettingsPermissionDeniedMessage("إصلاح قاعدة البيانات");
                return;
            }

            try
            {
                IsLoading = true;

                var result = MessageBox.Show(
                    "سيتم إصلاح البيانات التالفة في قاعدة البيانات.\n\n" +
                    "هذا سيحول الحقول الرقمية غير الصحيحة إلى قيم فارغة.\n\n" +
                    "هل تريد المتابعة؟",
                    "تأكيد إصلاح البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    using var scope = _serviceProvider.CreateScope();
                    var repairService = scope.ServiceProvider.GetRequiredService<IDatabaseRepairService>();

                    var success = await repairService.FixDeviceNumericFieldsAsync();

                    if (success)
                    {
                        MessageBox.Show(
                            "✅ تم إصلاح البيانات التالفة بنجاح!\n\n" +
                            "يمكنك الآن تصفح الأجهزة بدون أخطاء.",
                            "تم الإصلاح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في إصلاح البيانات.\n\nتحقق من اتصال قاعدة البيانات.",
                            "فشل الإصلاح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إصلاح البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"RepairDatabaseAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CreateBackupAsync()
        {
            // التحقق من صلاحية إنشاء النسخ الاحتياطية
            if (!CanCreateBackup)
            {
                SettingsHelper.ShowSettingsPermissionDeniedMessage("إنشاء النسخ الاحتياطية");
                return;
            }

            try
            {
                IsLoading = true;

                var saveFileDialog = new SaveFileDialog
                {
                    Title = "حفظ النسخة الاحتياطية",
                    Filter = "SQL Files (*.sql)|*.sql",
                    DefaultExt = "sql",
                    FileName = $"backup_{MySQLDatabase}_{DateTime.Now:yyyyMMdd_HHmmss}.sql"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // إنشاء scope جديد للحصول على DatabaseService
                    using var scope = _serviceProvider.CreateScope();
                    var databaseService = scope.ServiceProvider.GetRequiredService<IDatabaseService>();

                    var success = await databaseService.BackupDatabaseAsync(saveFileDialog.FileName);

                    if (success)
                    {
                        // Update last backup date
                        LastBackupDate = DateTime.Now;
                        LastBackupDateDisplay = LastBackupDate.ToString("dd/MM/yyyy HH:mm");

                        MessageBox.Show(
                            "✅ تم إنشاء النسخة الاحتياطية بنجاح!",
                            "نجح النسخ الاحتياطي",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في إنشاء النسخة الاحتياطية.",
                            "فشل النسخ الاحتياطي",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في النسخ الاحتياطي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"CreateBackupAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task SaveSettingsAsync()
        {
            try
            {
                IsLoading = true;

                // إنشاء scope جديد للحصول على SettingsService
                using var scope = _serviceProvider.CreateScope();
                var settingsService = scope.ServiceProvider.GetRequiredService<ISettingsService>();

                // إنشاء إعدادات Services.AppSettings للحفظ
                var serviceSettings = new Services.AppSettings
                {
                    RememberLogin = RememberLoginEnabled,
                    DefaultNetwork = DefaultNetwork,
                    PingTimeout = PingTimeoutSeconds,
                    ShowNotifications = ShowNotifications,
                    Theme = Theme,
                    Language = Language,
                    BackupLocation = BackupLocation,
                    AutoBackupEnabled = AutoBackupEnabled,
                    AutoBackupDays = AutoBackupDays
                };

                // إضافة إعدادات قاعدة البيانات فقط إذا كان المستخدم لديه صلاحية
                if (CanEditDatabaseSettings)
                {
                    serviceSettings.MySQLServer = MySQLServer;
                    serviceSettings.MySQLDatabase = MySQLDatabase;
                    serviceSettings.MySQLUser = MySQLUser;
                    serviceSettings.MySQLPassword = MySQLPassword;
                }

                await settingsService.SaveSettingsAsync(serviceSettings);

                // تطبيق المظهر الجديد
                await _themeService.ApplyThemeAsync(Theme);

                // تطبيق اللغة الجديدة
                await _localizationService.ApplyLanguageAsync(Language);

                MessageBox.Show("تم حفظ الإعدادات بنجاح", "تم الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"SaveSettingsAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }





        [RelayCommand]
        private void SelectBackupLocation()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "اختر مجلد النسخ الاحتياطية",
                    FileName = "اختر هذا المجلد",
                    Filter = "Folder|*.folder",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    InitialDirectory = BackupLocation
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    BackupLocation = Path.GetDirectoryName(saveFileDialog.FileName) ?? BackupLocation;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار المجلد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task RestoreBackupAsync()
        {
            // التحقق من صلاحية استعادة النسخ الاحتياطية
            if (!CanRestoreBackup)
            {
                SettingsHelper.ShowSettingsPermissionDeniedMessage("استعادة النسخ الاحتياطية");
                return;
            }

            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار النسخة الاحتياطية",
                    Filter = "SQL Files (*.sql)|*.sql|All Files (*.*)|*.*",
                    DefaultExt = "sql"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "⚠️ تحذير: سيتم استبدال قاعدة البيانات الحالية بالكامل!\n\n" +
                        "هل تريد المتابعة؟",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        IsLoading = true;

                        // إنشاء scope جديد للحصول على DatabaseService
                        using var scope = _serviceProvider.CreateScope();
                        var databaseService = scope.ServiceProvider.GetRequiredService<IDatabaseService>();

                        var success = await databaseService.RestoreDatabaseAsync(openFileDialog.FileName);

                        if (success)
                        {
                            MessageBox.Show(
                                "✅ تم استعادة قاعدة البيانات بنجاح!",
                                "نجحت الاستعادة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);

                            // Reload statistics after restore
                            await LoadFilteredStatisticsAsync();
                        }
                        else
                        {
                            MessageBox.Show(
                                "❌ فشل في استعادة قاعدة البيانات.",
                                "فشلت الاستعادة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"RestoreBackupAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task UpdateDatabaseStructureAsync()
        {
            // التحقق من صلاحية تحديث بنية قاعدة البيانات
            if (!CanUpdateDatabaseStructure)
            {
                SettingsHelper.ShowSettingsPermissionDeniedMessage("تحديث بنية قاعدة البيانات");
                return;
            }

            try
            {
                var result = MessageBox.Show(
                    "سيتم تحديث بنية قاعدة البيانات لجعل NetworkId و SiteId اختياري.\n\n" +
                    "هذا آمن ولن يؤثر على البيانات الموجودة.\n\n" +
                    "هل تريد المتابعة؟",
                    "تحديث بنية قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;

                    // إنشاء scope جديد للحصول على DatabaseService
                    using var scope = _serviceProvider.CreateScope();
                    var databaseService = scope.ServiceProvider.GetRequiredService<IDatabaseService>();

                    // تشغيل ملف التحديث المخصص
                    var updateFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "UpdateDatabase.sql");

                    bool success = false;
                    if (File.Exists(updateFilePath))
                    {
                        success = await databaseService.ExecuteSqlFileAsync(updateFilePath);
                    }
                    else
                    {
                        // Fallback to the old method
                        success = await databaseService.CreateTablesAsync();
                    }

                    if (success)
                    {
                        MessageBox.Show(
                            "✅ تم تحديث بنية قاعدة البيانات بنجاح!\n\n" +
                            "الآن يمكن إضافة المواقع والمستخدمين والمهام والمشتريات والمخزون بدون مشاكل.",
                            "تم التحديث",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);

                        // Reload connection status
                        await TestDatabaseConnectionAsync();
                    }
                    else
                    {
                        MessageBox.Show(
                            "❌ فشل في تحديث بنية قاعدة البيانات.",
                            "فشل التحديث",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث بنية قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"UpdateDatabaseStructureAsync error: {ex}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task DeleteDatabaseAsync()
        {
            try
            {
                var result = MessageBox.Show(
                    "⚠️ تحذير خطير!\n\n" +
                    $"سيتم حذف قاعدة البيانات '{MySQLDatabase}' نهائياً!\n" +
                    "جميع البيانات ستفقد ولا يمكن استرجاعها.\n\n" +
                    "هل أنت متأكد من أنك تريد المتابعة؟",
                    "تأكيد حذف قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var confirmResult = MessageBox.Show(
                        "تأكيد نهائي!\n\n" +
                        "هذا الإجراء لا يمكن التراجع عنه.\n" +
                        "هل تريد حذف قاعدة البيانات فعلاً؟",
                        "تأكيد نهائي",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Stop);

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        IsLoading = true;

                        // إنشاء scope جديد للحصول على DatabaseService
                        using var scope = _serviceProvider.CreateScope();
                        var databaseService = scope.ServiceProvider.GetRequiredService<IDatabaseService>();

                        var success = await databaseService.DeleteDatabaseAsync();

                        if (success)
                        {
                            IsDatabaseConnected = false;
                            DatabaseConnectionStatus = "تم حذف قاعدة البيانات";

                            // Reset statistics
                            TotalDevices = 0;
                            TotalSites = 0;
                            TotalUsers = 0;
                            TotalPurchases = 0;
                            TotalInventoryItems = 0;

                            MessageBox.Show(
                                "✅ تم حذف قاعدة البيانات بنجاح!",
                                "تم الحذف",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show(
                                "❌ فشل في حذف قاعدة البيانات.",
                                "فشل الحذف",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ResetSettingsAsync()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;

                    // إنشاء scope جديد للحصول على SettingsService
                    using var scope = _serviceProvider.CreateScope();
                    var settingsService = scope.ServiceProvider.GetRequiredService<ISettingsService>();

                    await settingsService.ResetSettingsAsync();
                    await LoadSettingsAsync();
                    MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAccessSettings));
                    OnPropertyChanged(nameof(CanCreateBackup));
                    OnPropertyChanged(nameof(CanRestoreBackup));
                    OnPropertyChanged(nameof(CanUpdateDatabaseStructure));
                    OnPropertyChanged(nameof(CanEditDatabaseSettings));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadSettingsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _authService.UserChanged -= OnUserChanged;
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _loadSemaphore?.Dispose();
        }

        // SQLite functions removed - using MySQL only



        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // معالج تغيير المظهر فورياً
        partial void OnThemeChanged(string value)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await _themeService.ApplyThemeAsync(value);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error applying theme: {ex.Message}");
                }
            });
        }

        // معالج تغيير اللغة فورياً
        partial void OnLanguageChanged(string value)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await _localizationService.ApplyLanguageAsync(value);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error applying language: {ex.Message}");
                }
            });
        }
    }
}
