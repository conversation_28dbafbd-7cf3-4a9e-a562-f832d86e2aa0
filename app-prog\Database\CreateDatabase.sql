-- إنشاء قاعدة بيانات نظام إدارة الشبكات المثالية
-- مطابقة تماماً للنماذج في الكود بدون أخطاء

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS NetworkManagementDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE NetworkManagementDB;

-- جدول الشبكات
CREATE TABLE IF NOT EXISTS Networks (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Color VARCHAR(20) DEFAULT '#2196F3',
    IsActive BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    UNIQUE KEY unique_name (Name)
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS Users (
    Id VARCHAR(50) PRIMARY KEY,
    Username VARCHAR(50) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    Name VARCHAR(100) NOT NULL,
    Email VARCHAR(100),
    Role VARCHAR(50) NOT NULL,
    NetworkId VARCHAR(50),
    IsActive BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_username (Username),
    INDEX idx_role (Role),
    INDEX idx_network (NetworkId)
);

-- جدول المواقع (مطابق تماماً لـ Site.cs Model)
CREATE TABLE IF NOT EXISTS Sites (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Address VARCHAR(200),
    Phone VARCHAR(20),
    GpsLat DOUBLE,
    GpsLng DOUBLE,
    PowerSource VARCHAR(100),
    StorageCapacity INT,
    DailyConsumption INT,
    InstallationBase VARCHAR(100),
    Boxes INT,
    WireLength INT,
    AssociatedDeviceIds TEXT,
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_name (Name),
    INDEX idx_network (NetworkId)
);

-- جدول الأجهزة (مطابق تماماً لـ Device.cs Model)
CREATE TABLE IF NOT EXISTS Devices (
    Id VARCHAR(50) PRIMARY KEY,
    Responsible VARCHAR(100),
    Type VARCHAR(50),
    Location VARCHAR(200),
    Phone VARCHAR(20),
    Ip VARCHAR(15),
    InstallDate DATETIME,
    PowerConnection VARCHAR(100),
    AdapterType VARCHAR(50),
    NetworkCableLength INT,
    PowerCableLength INT,
    ConnectionMethod VARCHAR(50),
    LinkedNetwork VARCHAR(50),
    BroadcastNetworkName VARCHAR(100),
    Channel INT,
    ConnectedDevices INT,
    Status VARCHAR(20) DEFAULT 'active',
    LastCheck DATETIME,
    SiteId VARCHAR(50),
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (SiteId) REFERENCES Sites(Id) ON DELETE SET NULL,
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_ip (Ip),
    INDEX idx_status (Status),
    INDEX idx_type (Type),
    INDEX idx_site (SiteId),
    INDEX idx_network (NetworkId)
);

-- جدول المهام (مطابق تماماً لـ Task.cs Model)
CREATE TABLE IF NOT EXISTS Tasks (
    Id VARCHAR(50) PRIMARY KEY,
    UserId VARCHAR(50) NOT NULL,
    Description VARCHAR(1000) NOT NULL,
    Date DATETIME NOT NULL,
    Status VARCHAR(50) DEFAULT 'pending',
    Notes TEXT,
    Priority VARCHAR(50) DEFAULT 'medium',
    DueDate DATETIME,
    CompletedAt DATETIME,
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_user (UserId),
    INDEX idx_status (Status),
    INDEX idx_network (NetworkId),
    INDEX idx_date (Date)
);

-- جدول المشتريات (مطابق تماماً لـ Purchase.cs Model)
CREATE TABLE IF NOT EXISTS Purchases (
    Id VARCHAR(50) PRIMARY KEY,
    ItemType VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Quantity INT DEFAULT 1,
    Unit VARCHAR(50),
    Price DECIMAL(10,2),
    Supplier VARCHAR(200),
    InvoiceNumber VARCHAR(100),
    Category VARCHAR(100),
    Date DATETIME DEFAULT NOW(),
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_item_type (ItemType),
    INDEX idx_category (Category),
    INDEX idx_date (Date),
    INDEX idx_network (NetworkId)
);

-- جدول المخزون (مطابق تماماً لـ Inventory.cs Model)
CREATE TABLE IF NOT EXISTS Inventory (
    Id VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Description VARCHAR(500),
    Quantity INT DEFAULT 0,
    Unit VARCHAR(50),
    UnitPrice DECIMAL(10,2),
    Location VARCHAR(200),
    Supplier VARCHAR(200),
    MinimumStock INT,
    MaximumStock INT,
    Category VARCHAR(100),
    LastUpdated DATETIME DEFAULT NOW(),
    NetworkId VARCHAR(50),
    CreatedAt DATETIME DEFAULT NOW(),
    UpdatedAt DATETIME DEFAULT NOW(),
    FOREIGN KEY (NetworkId) REFERENCES Networks(Id) ON DELETE SET NULL,
    INDEX idx_name (Name),
    INDEX idx_category (Category),
    INDEX idx_network (NetworkId)
);

-- إدراج البيانات الأولية
-- المستخدم المدير العام فقط
INSERT IGNORE INTO Users (Id, Username, Password, Name, Role, NetworkId)
VALUES ('admin', 'admin', 'password', 'المدير العام', 'Admin', NULL);
