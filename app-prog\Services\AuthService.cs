using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class AuthService : IAuthService
    {
        private User? _currentUser;
        private readonly IServiceProvider _serviceProvider;
        private readonly IPasswordHashService _passwordHashService;
        private readonly object _userLock = new object();
        private DateTime _lastUserUpdate = DateTime.MinValue;
        private readonly TimeSpan _userCacheTimeout = TimeSpan.FromMinutes(5);

        public AuthService(IServiceProvider serviceProvider, IPasswordHashService passwordHashService)
        {
            _serviceProvider = serviceProvider;
            _passwordHashService = passwordHashService;
        }

        public User? CurrentUser
        {
            get
            {
                lock (_userLock)
                {
                    return _currentUser;
                }
            }
        }
        public bool IsLoggedIn => _currentUser != null;

        public event EventHandler<User?>? UserChanged;

        /// <summary>
        /// تحديث بيانات المستخدم الحالي من قاعدة البيانات بشكل آمن
        /// </summary>
        public async System.Threading.Tasks.Task RefreshCurrentUserAsync()
        {
            User? userToRefresh;
            lock (_userLock)
            {
                userToRefresh = _currentUser;
            }

            if (userToRefresh != null)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();
                    if (context.Users != null)
                    {
                        var updatedUser = await context.Users
                            .Include(u => u.Network)
                            .FirstOrDefaultAsync(u => u.Id == userToRefresh.Id)
                            .ConfigureAwait(false);

                        if (updatedUser != null)
                        {
                            lock (_userLock)
                            {
                                _currentUser = updatedUser;
                                _lastUserUpdate = DateTime.Now;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error refreshing current user: {ex.Message}");
                }
            }
        }

        public async Task<User?> LoginAsync(string username, string password)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

                if (context.Users == null)
                {
                    return null;
                }

                // البحث عن المستخدم بالاسم فقط
                var user = await context.Users
                    .Include(u => u.Network)
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user != null)
                {
                    // التحقق من كلمة المرور
                    bool isPasswordValid = false;

                    // التحقق من أن كلمة المرور مشفرة أم لا
                    if (_passwordHashService.IsPasswordHashed(user.Password))
                    {
                        // كلمة المرور مشفرة - استخدم التحقق المشفر
                        isPasswordValid = _passwordHashService.VerifyPassword(password, user.Password);
                    }
                    else
                    {
                        // كلمة المرور غير مشفرة (للتوافق مع البيانات القديمة)
                        isPasswordValid = user.Password == password;

                        // إذا كانت كلمة المرور صحيحة، قم بتشفيرها وحفظها
                        if (isPasswordValid)
                        {
                            user.Password = _passwordHashService.HashPassword(password);
                            user.UpdatedAt = DateTime.Now;
                            await context.SaveChangesAsync();
                        }
                    }

                    if (isPasswordValid)
                    {
                        lock (_userLock)
                        {
                            _currentUser = user;
                            _lastUserUpdate = DateTime.Now;
                        }
                        UserChanged?.Invoke(this, _currentUser);
                        return user;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during login: {ex.Message}");
                return null;
            }
        }

        public async System.Threading.Tasks.Task<bool> LogoutAsync()
        {
            try
            {
                lock (_userLock)
                {
                    _currentUser = null;
                    _lastUserUpdate = DateTime.MinValue;
                }
                UserChanged?.Invoke(this, null);
                return await System.Threading.Tasks.Task.FromResult(true);
            }
            catch
            {
                return false;
            }
        }

        // Permissions and Role Checks
        public bool IsAdmin => CurrentUser?.Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true;
        public bool IsManager => CurrentUser?.Role?.Equals("Manager", StringComparison.OrdinalIgnoreCase) == true;
        public bool IsTechnician => CurrentUser?.Role?.Equals("Technician", StringComparison.OrdinalIgnoreCase) == true;
        public bool IsUser => CurrentUser?.Role?.Equals("User", StringComparison.OrdinalIgnoreCase) == true;

        // Admin without NetworkId has full access to all networks
        public bool IsSuperAdmin => IsAdmin && string.IsNullOrEmpty(CurrentUser?.NetworkId);

        // Manager with NetworkId has full access to their network only
        public bool IsNetworkManager => IsManager && !string.IsNullOrEmpty(CurrentUser?.NetworkId);

        public bool CanManageNetworks => IsSuperAdmin; // Only Super Admin can manage networks
        public bool CanManageUsers => IsSuperAdmin || IsNetworkManager; // For backward compatibility
        public bool CanViewAllNetworks => IsSuperAdmin; // Only Super Admin sees all networks
        public bool CanManageOwnNetwork => IsNetworkManager || IsTechnician;
        public bool CanViewSettings => IsSuperAdmin || IsNetworkManager; // Both can access settings
        public bool CanManagePurchases => IsSuperAdmin || IsNetworkManager; // Both can manage purchases
        public bool CanManageInventory => IsSuperAdmin || IsNetworkManager; // Both can manage inventory
        public bool CanManageTasks => IsSuperAdmin || IsNetworkManager; // Both can manage tasks
        public bool CanManageSites => IsSuperAdmin || IsNetworkManager; // Both can manage sites
        public bool CanManageDevices => IsSuperAdmin || IsNetworkManager; // Both can manage devices
        public bool CanViewReports => IsSuperAdmin || IsNetworkManager || IsTechnician; // All logged users can view reports

        public string? CurrentUserNetworkId => CurrentUser?.NetworkId;
        public string? CurrentUserNetworkName => CurrentUser?.Network?.Name;

        public bool CanAccessNetwork(string networkId)
        {
            if (IsSuperAdmin) return true;
            return CurrentUserNetworkId == networkId;
        }

        public bool CanEditData(string? dataNetworkId = null)
        {
            if (IsSuperAdmin) return true;
            if (IsManager || IsTechnician)
            {
                return dataNetworkId == null || dataNetworkId == CurrentUserNetworkId;
            }
            return false;
        }

        public bool CanAddData(string? targetNetworkId = null)
        {
            // Super Admin يمكنه إضافة البيانات في أي شبكة
            if (IsSuperAdmin) return true;

            // Network Manager يمكنه إضافة البيانات في شبكته فقط
            if (IsNetworkManager)
            {
                return targetNetworkId == null || targetNetworkId == CurrentUserNetworkId;
            }

            // Technician يمكنه إضافة البيانات في شبكته فقط
            if (IsTechnician)
            {
                return targetNetworkId == null || targetNetworkId == CurrentUserNetworkId;
            }

            // المستخدمون العاديون لا يمكنهم إضافة البيانات
            return false;
        }

        public bool CanViewData(string? dataNetworkId = null)
        {
            if (IsSuperAdmin) return true;
            if (IsManager || IsTechnician)
            {
                return dataNetworkId == null || dataNetworkId == CurrentUserNetworkId;
            }
            return false;
        }

        public bool CanDeleteData(string? dataNetworkId = null)
        {
            // فقط Super Admin و Network Manager يمكنهم حذف البيانات
            if (IsSuperAdmin) return true;
            if (IsNetworkManager)
            {
                return dataNetworkId == null || dataNetworkId == CurrentUserNetworkId;
            }
            return false;
        }

        public bool CanManageUsersInNetwork(string? targetNetworkId = null)
        {
            // Super Admin يمكنه إدارة جميع المستخدمين
            if (IsSuperAdmin) return true;

            // Network Manager يمكنه إدارة مستخدمي شبكته فقط
            if (IsNetworkManager)
            {
                return targetNetworkId == null || targetNetworkId == CurrentUserNetworkId;
            }

            return false;
        }

        public bool CanEditUser(User targetUser)
        {
            if (targetUser == null) return false;

            // Super Admin يمكنه تعديل أي مستخدم
            if (IsSuperAdmin) return true;

            // Network Manager يمكنه تعديل مستخدمي شبكته فقط (ولكن ليس Admin أو Manager آخر)
            if (IsNetworkManager)
            {
                // لا يمكن تعديل Admin أو Manager آخر
                if (targetUser.Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true ||
                    targetUser.Role?.Equals("Manager", StringComparison.OrdinalIgnoreCase) == true)
                {
                    return false;
                }

                return targetUser.NetworkId == CurrentUserNetworkId;
            }

            // المستخدم يمكنه تعديل بياناته الشخصية فقط
            return CurrentUser?.Id == targetUser.Id;
        }

        public bool CanDeleteUser(User targetUser)
        {
            if (targetUser == null) return false;

            // لا يمكن حذف النفس
            if (CurrentUser?.Id == targetUser.Id) return false;

            // فقط Super Admin يمكنه حذف المستخدمين
            if (IsSuperAdmin) return true;

            return false;
        }
    }
}
